import asyncio
import json
import urllib
from datetime import datetime, timedelta, timezone
from typing import Any, Dict, List, Optional, Tuple

from api.db.database import get_session
from api.models.annotation_task import Annotation, AnnotationTask
from api.models.task import (
    AssignTaskRequest,
    BatchGetXpathSummaryRequest,
    CreateTaskRequest,
    DeleteTaskRequest,
    FinishTaskRequest,
    GetUbfRequest,
    GetXpathSummaryRequest,
    HasPermissionRequest,
    ResetTaskRequest,
    StartTaskRequest,
    Task,
    UnprocessableTaskRequest,
    UpdateTaskRequest,
)
from api.models.task_status import TaskStatus
from api.services.session_service import SessionService
from api.utils.rainbow.client import get_rainbow_config
from api.utils.svrkit.ilogs import logger
from api.utils.svrkit.ttlkv import (
    batch_get_sum_ttlkv,
    generate_key,
    get_sum_ttlkv,
    get_ttlkv,
    set_ttlkv,
)
from api.utils.svrkit.ubf import get_user_profile_json
from api.utils.time import get_shanghai_time, get_shanghai_time_tz
from api.utils.wecube.log import log
from fastapi import HTTPException
from sqlalchemy import and_, func, select, update
from sqlalchemy.future import select
from svrkit.core.oss import OssAttrInc


class AnnotationTaskService:
    @staticmethod
    async def create_task(request: CreateTaskRequest) -> int:
        async with get_session() as session:
            task = AnnotationTask(
                app_id=request.app_id,
                app_name=request.app_name,
                query=request.query,
                status=TaskStatus.INITIAL.value,
                tag=request.tag,
            )
            session.add(task)
            await session.flush()
            return task.id

    @staticmethod
    async def list_tasks(
        assignee: Optional[str] = None,
        app_id: Optional[str] = None,
        status: Optional[str] = None,
        is_deleted: Optional[bool] = None,
        is_backfilled: Optional[bool] = None,
        task_ids: Optional[List[int]] = None,
        tag: Optional[str] = None,
        page: int = 1,
        page_size: int = 10,
    ) -> Dict[str, Any]:
        async with get_session() as session:
            # 基础查询（用于获取总数）
            count_query = select(func.count()).select_from(AnnotationTask)

            # 基础查询（用于获取分页数据）
            data_query = select(AnnotationTask).order_by(
                AnnotationTask.created_at.desc()
            )

            # 添加筛选条件（同时应用到两个查询）
            conditions = []
            if assignee is not None:
                if assignee.strip() in ("null", ""):
                    assignee = None
                conditions.append(AnnotationTask.assignee == assignee)
            if app_id is not None:
                conditions.append(AnnotationTask.app_id == app_id)
            if status is not None:
                conditions.append(AnnotationTask.status == status)
            if is_deleted is not None:
                conditions.append(AnnotationTask.is_deleted == is_deleted)
            if is_backfilled is not None:
                if is_backfilled == True:
                    conditions.append(AnnotationTask.backfilled_annotation_id != None)
                else:
                    conditions.append(AnnotationTask.backfilled_annotation_id == None)
            if task_ids is not None and len(task_ids) > 0:
                conditions.append(AnnotationTask.id.in_(task_ids))
            if tag is not None:
                conditions.append(AnnotationTask.tag == tag)
            if conditions:
                count_query = count_query.where(*conditions)
                data_query = data_query.where(*conditions)

            # 执行总数查询
            total_result = await session.execute(count_query)
            total = total_result.scalar_one()

            # 执行分页查询
            offset = (page - 1) * page_size
            paginated_query = data_query.offset(offset).limit(page_size)
            data_result = await session.execute(paginated_query)
            tasks = data_result.scalars().all()

            # 返回任务列表和总记录数
            return {
                "tasks": [Task(**task.to_dict()) for task in tasks],
                "total": total,
                "page": page,
                "page_size": page_size,
            }

    @staticmethod
    async def get_task(task_id: int) -> Task:
        async with get_session() as session:
            result = await session.execute(
                select(AnnotationTask).where(AnnotationTask.id == task_id)
            )
            task = result.scalar_one_or_none()

            if not task:
                raise HTTPException(status_code=404, detail="Task not found")

            return Task(**task.to_dict())

    @staticmethod
    async def assign_tasks(request: AssignTaskRequest) -> bool:
        async with get_session() as session:
            stmt = (
                update(AnnotationTask)
                .where(
                    and_(
                        AnnotationTask.id.in_(request.task_ids),
                        AnnotationTask.status == TaskStatus.INITIAL.value,
                    )
                )
                .values(assignee=request.assignee)
            )
            await session.execute(stmt)
            return True

    @staticmethod
    async def start_task(request: StartTaskRequest) -> bool:
        async with get_session() as session:
            # First get the task to check assignee
            result = await session.execute(
                select(AnnotationTask).where(AnnotationTask.id == request.task_id)
            )
            task = result.scalar_one_or_none()

            if not task:
                raise HTTPException(status_code=404, detail="Task not found")

            if task.assignee != request.rtx:
                raise HTTPException(
                    status_code=403, detail="Only the assigned user can start this task"
                )

            stmt = (
                update(AnnotationTask)
                .where(
                    AnnotationTask.id == request.task_id,
                    AnnotationTask.status == TaskStatus.INITIAL.value,
                )
                .values(status=TaskStatus.RUNNING.value, start_time=request.start_time)
            )
            result = await session.execute(stmt)
            if result.rowcount == 0:
                raise HTTPException(status_code=400, detail="Task cannot be started")
            return True

    @staticmethod
    async def finish_task(request: FinishTaskRequest) -> bool:
        async with get_session() as session:
            # First get the task to check assignee
            result = await session.execute(
                select(AnnotationTask).where(AnnotationTask.id == request.task_id)
            )
            task = result.scalar_one_or_none()

            if not task:
                raise HTTPException(status_code=404, detail="Task not found")

            if task.assignee != request.rtx:
                raise HTTPException(
                    status_code=403,
                    detail="Only the assigned user can finish this task",
                )

            # 去标注流水里查看是否已有
            rtx = task.assignee
            app_id = task.app_id
            instruction = task.query

            start_time_ = task.start_time.astimezone(
                timezone(timedelta(hours=8))
            ).replace(tzinfo=None) + timedelta(hours=8)
            end_time_ = request.end_time.replace(tzinfo=None) + timedelta(hours=8)
            logger.info(f"finish {start_time_}, {end_time_}")
            candidates = await session.execute(
                select(Annotation)
                .where(Annotation.rtx == rtx)
                .where(Annotation.app_id == app_id)
                .where(Annotation.instruction == instruction)
                .where(Annotation.is_deleted == 0)
                .where(Annotation.submitted_at >= start_time_)
                .where(Annotation.submitted_at <= end_time_)
                .order_by(Annotation.id.desc())
                .limit(1)
            )
            candidate = candidates.scalar_one_or_none()
            if candidate:
                # 将这条绑定过来，避免重复回流
                await session.execute(
                    update(Annotation)
                    .where(Annotation.id == candidate.id)
                    .values(target_id=str(task.id))
                )
                logger.info(f"Found candidate {candidate.submitted_at}")
                backfill_id = candidate.id
                stmt = (
                    update(AnnotationTask)
                    .where(
                        AnnotationTask.id == request.task_id,
                        AnnotationTask.status == TaskStatus.RUNNING.value,
                    )
                    .values(
                        status=TaskStatus.SUCCESS.value,
                        end_time=request.end_time,
                        backfilled_annotation_id=backfill_id,
                    )
                )
            else:
                logger.info("No candidate found")
                stmt = (
                    update(AnnotationTask)
                    .where(
                        AnnotationTask.id == request.task_id,
                        AnnotationTask.status == TaskStatus.RUNNING.value,
                    )
                    .values(status=TaskStatus.SUCCESS.value, end_time=request.end_time)
                )
            result = await session.execute(stmt)
            if result.rowcount == 0:
                raise HTTPException(status_code=400, detail="Task cannot be finished")
            return True

    @staticmethod
    async def delete_task(request: DeleteTaskRequest) -> bool:
        async with get_session() as session:
            # First check if the task exists
            result = await session.execute(
                select(AnnotationTask).where(AnnotationTask.id == request.task_id)
            )
            task = result.scalar_one_or_none()

            if not task:
                raise HTTPException(status_code=404, detail="Task not found")

            # Soft delete related annotations by target_id
            annotation_stmt = (
                update(Annotation)
                .where(Annotation.target_id == str(request.task_id))
                .values(is_deleted=1)
            )
            await session.execute(annotation_stmt)

            # Update the is_deleted flag
            stmt = (
                update(AnnotationTask)
                .where(AnnotationTask.id == request.task_id)
                .values(is_deleted=True)
            )
            await session.execute(stmt)
            return True

    @staticmethod
    async def reset_task(request: ResetTaskRequest) -> bool:
        async with get_session() as session:
            # First check if the task exists
            result = await session.execute(
                select(AnnotationTask).where(AnnotationTask.id == request.task_id)
            )
            task = result.scalar_one_or_none()

            if not task:
                raise HTTPException(status_code=404, detail="Task not found")

            # Soft delete related annotations by target_id
            annotation_stmt = (
                update(Annotation)
                .where(Annotation.target_id == str(request.task_id))
                .values(is_deleted=1)
            )
            await session.execute(annotation_stmt)

            # Reset task to initial state
            stmt = (
                update(AnnotationTask)
                .where(AnnotationTask.id == request.task_id)
                .values(
                    status=TaskStatus.INITIAL.value,
                    start_time=None,
                    end_time=None,
                    backfilled_annotation_id=None,
                )
            )

            await session.execute(stmt)
            return True

    @staticmethod
    async def update_task(request: UpdateTaskRequest) -> bool:
        async with get_session() as session:
            # First check if the task exists
            result = await session.execute(
                select(AnnotationTask).where(AnnotationTask.id == request.task_id)
            )
            task = result.scalar_one_or_none()

            if not task:
                raise HTTPException(status_code=404, detail="Task not found")

            # Build update values dict with only provided fields
            update_values = {}
            if request.query is not None:
                update_values["query"] = request.query
            if request.app_id is not None:
                update_values["app_id"] = request.app_id
            if request.app_name is not None:
                update_values["app_name"] = request.app_name
            if request.tag is not None:
                update_values["tag"] = request.tag

            # Only update if there are fields to update
            if not update_values:
                raise HTTPException(
                    status_code=400, detail="No fields provided for update"
                )

            # Update the task
            stmt = (
                update(AnnotationTask)
                .where(AnnotationTask.id == request.task_id)
                .values(**update_values)
            )
            await session.execute(stmt)
            return True

    @staticmethod
    async def has_permission(request: HasPermissionRequest) -> bool:
        try:
            config = get_rainbow_config()
            admin_members = config.get("admin_members", [])
            if request.rtx in admin_members:
                return True
            else:
                return False
        except Exception:
            return False

    @staticmethod
    async def get_ubf(request: GetUbfRequest) -> Dict[str, Any]:
        try:
            ubf = get_user_profile_json(request.uin)
            return ubf
        except Exception:
            return {}

    @staticmethod
    async def get_xpath_summary(request: GetXpathSummaryRequest) -> str:
        try:
            summary = get_sum_ttlkv(request.app_id, request.xpath)
            if summary is None or summary == "":
                OssAttrInc(535937, 40, 1)  # kv空
            else:
                OssAttrInc(535937, 36, 1)  # kv非空
            return summary
        except Exception as e:
            OssAttrInc(535937, 37, 1)  # kv异常
            log(f"[llm_summary] Error getting XML summary: {e}")
            return ""

    @staticmethod
    async def batch_xpath_summary(
        request: BatchGetXpathSummaryRequest,
    ) -> Dict[str, str]:
        try:
            xpaths = list(set(request.xpaths))
            OssAttrInc(535937, 45, len(xpaths))
            xpath_summary_map = batch_get_sum_ttlkv(
                request.app_id, xpaths, request.prefix
            )
            empty = {k: v for k, v in xpath_summary_map.items() if not v}
            OssAttrInc(535937, 46, len(empty))
            OssAttrInc(535937, 49, len(xpaths) - len(empty))
            return xpath_summary_map
        except Exception as e:
            OssAttrInc(535937, 47, 1)
            log(f"[llm_summary_v2] Error getting XML summary: {e}")
            return {}

    @staticmethod
    async def backfill_tasks(task_ids: Optional[List[int]] = None) -> Dict[str, Any]:
        try:
            # 1. 先获取需要处理的任务
            tasks = await AnnotationTaskService._fetch_tasks_to_process(
                task_ids=task_ids
            )
            if not tasks:
                return {
                    "success": True,
                    "processed_count": 0,
                    "message": "没有找到需要回填的已完成任务",
                }

            OssAttrInc(533817, 0, len(tasks))

            # 2. 并行处理任务
            config = get_rainbow_config()
            rtx2uid = json.loads(config.get("rtx2uid", "{}"))

            batch_size = 1
            task_results = []

            for i in range(0, len(tasks), batch_size):
                batch = tasks[i : i + batch_size]
                # 并行处理每一批任务
                batch_tasks = [
                    AnnotationTaskService._process_single_task_for_backfill(
                        task, None, rtx2uid
                    )
                    for task in batch
                ]
                batch_results = await asyncio.gather(
                    *batch_tasks, return_exceptions=True
                )
                task_results.extend(batch_results)

                # 在批次之间添加小延迟，避免系统负载过高
                await asyncio.sleep(0.1)

            # 处理结果
            task_processing_records = {}
            failed_tasks = []
            failed_count = 0

            for task, result in zip(tasks, task_results):
                if isinstance(result, Exception):
                    failed_count += 1
                    failed_tasks.append(
                        {"task_id": task.id, "reason": f"处理异常: {str(result)}"}
                    )
                    continue

                success, record, reason = result
                if success:
                    task_processing_records[task.id] = record
                else:
                    failed_count += 1
                    failed_tasks.append({"task_id": task.id, "reason": reason})

            # 3. 每个任务单独验证和提交（原子化处理）
            excluded_count = 0
            excluded_tasks = []
            final_processed_count = 0

            for task_id, record in task_processing_records.items():
                async with get_session() as session:
                    try:
                        # 验证任务状态
                        current_task_stmt = select(AnnotationTask).where(
                            AnnotationTask.id == task_id
                        )
                        current_task_result = await session.execute(current_task_stmt)
                        current_task = current_task_result.scalar_one_or_none()

                        if not current_task:
                            excluded_count += 1
                            excluded_tasks.append(
                                {"task_id": task_id, "reason": "任务已被删除"}
                            )
                            continue

                        if (
                            current_task.start_time != record["original_start"]
                            or current_task.end_time != record["original_end"]
                        ):
                            excluded_count += 1
                            excluded_tasks.append(
                                {"task_id": task_id, "reason": "时间发生变化"}
                            )
                            continue

                        # 新增：如果指定了task_ids且已有回流，软删旧回流记录
                        if (
                            task_ids is not None
                            and current_task.backfilled_annotation_id is not None
                        ):
                            # 软删 Annotation
                            await session.execute(
                                update(Annotation)
                                .where(
                                    Annotation.id
                                    == current_task.backfilled_annotation_id
                                )
                                .values(is_deleted=1)
                            )
                            # 清空回流ID
                            current_task.backfilled_annotation_id = None

                        # 验证通过，保存数据
                        annotation_request = record["annotation_request"]
                        save_result = await SessionService._save_annotation_with_operations_in_session(
                            session, annotation_request
                        )
                        current_task.backfilled_annotation_id = (
                            save_result.annotation_id
                        )

                        # 单个任务原子提交
                        await session.commit()
                        final_processed_count += 1
                        print(f"✅ 任务 {task_id} 处理成功")
                        log(f"✅ 任务 {task_id} 处理成功", task_id)

                    except Exception as e:
                        await session.rollback()
                        excluded_count += 1
                        excluded_tasks.append(
                            {"task_id": task_id, "reason": f"验证时出错: {str(e)}"}
                        )
                        print(f"❌ 任务 {task_id} 处理失败: {str(e)}")
                        log(f"❌ 任务 {task_id} 处理失败: {str(e)}", task_id)

            # 汇总处理结果
            final_failed_count = failed_count + excluded_count
            all_failed_tasks = failed_tasks + excluded_tasks

            print(f"\n💾 所有任务处理完成：")
            print(f"   - 成功处理: {final_processed_count} 个任务")
            print(f"   - 处理失败: {failed_count} 个任务")
            print(f"   - 时间变化排除: {excluded_count} 个任务")
            print(f"   - 总失败: {final_failed_count} 个任务")

            log(
                f"💾 所有任务处理完成："
                f"   - 成功处理: {final_processed_count} 个任务"
                f"   - 处理失败: {failed_count} 个任务"
                f"   - 时间变化排除: {excluded_count} 个任务"
                f"   - 总失败: {final_failed_count} 个任务"
            )

            OssAttrInc(533817, 2, failed_count)
            OssAttrInc(533817, 3, excluded_count)
            OssAttrInc(533817, 4, final_processed_count)

            return {
                "success": True,
                "processed_count": final_processed_count,
                "failed_count": final_failed_count,
                "failed_tasks": all_failed_tasks,
                "message": f"批处理完成，成功处理 {final_processed_count} 个任务，失败 {final_failed_count} 个",
            }

        except Exception as e:
            if "session" in locals():
                await session.rollback()
            raise HTTPException(status_code=500, detail=f"批处理失败: {str(e)}")

    @staticmethod
    async def get_session_online_live(
        assignee, start_time, end_time, cgi
    ) -> Dict[str, Any]:
        try:
            pass

        except Exception as e:
            log(f"获取会话数据时发生异常: {str(e)}")
            raise HTTPException(status_code=500, detail=f"获取会话数据异常: {str(e)}")

    @staticmethod
    async def get_session_online(assignee, id) -> Dict[str, Any]:
        try:
            async with get_session() as db:
                query = select(AnnotationTask).where(
                    AnnotationTask.status == TaskStatus.SUCCESS.value,
                    AnnotationTask.is_deleted == False,
                )
                if assignee:
                    assignees = assignee.split(",")
                    query = query.where(AnnotationTask.assignee.in_(assignees))
                if id:
                    ids = [int(x) for x in id.split(",")]
                    query = query.filter(AnnotationTask.id.in_(ids))
                result = await db.execute(query)
                tasks = result.scalars().all()
                config = get_rainbow_config()
                rtx2uid = json.loads(config.get("rtx2uid", "{}"))
                all_sessions = []
                for task in tasks:
                    try:
                        # ttlkv 半小时之前的，ttl长一些
                        key_ = generate_key(str(task.id))
                        content = get_ttlkv(key_)
                        if content:
                            all_sessions.append(json.loads(content))
                            continue
                        session_data = (
                            await SessionService._get_task_session_operations(
                                app_id=task.app_id,
                                user_id=rtx2uid.get(task.assignee).get("userid_"),
                                personal_userid=(
                                    rtx2uid.get(task.assignee).get("personal_userid_")
                                    if rtx2uid.get(task.assignee).get(
                                        "personal_userid_"
                                    )
                                    else rtx2uid.get(task.assignee).get("userid_")
                                ),
                                start_time=task.start_time,
                                end_time=task.end_time,
                                task_id=task.id,
                            )
                        )
                        session_data = [json.loads(data) for data in session_data]
                        for i in range(len(session_data)):
                            operation = session_data[i]
                            decoded_url = urllib.parse.unquote(
                                operation.get("bitmapurl_", "")
                            )
                            _, _, cos_url = SessionService.get_image_dimensions(
                                decoded_url, "ZwyOo"
                            )
                            operation["bitmapurl_"] = cos_url
                        session_ = {
                            "task_info": {
                                "task_id": task.id,
                                "app_id": task.app_id,
                                "app_name": task.app_name,
                                "query": task.query,
                                "assignee": task.assignee,
                                "time_range": {
                                    "start": (
                                        task.start_time + timedelta(hours=8)
                                    ).strftime("%Y-%m-%d %H:%M:%S"),
                                    "end": (
                                        task.end_time + timedelta(hours=8)
                                    ).strftime("%Y-%m-%d %H:%M:%S"),
                                },
                                "steps": len(session_data),
                            },
                            "operations": session_data,
                        }
                        all_sessions.append(session_)
                        is_30mins_ago = (
                            get_shanghai_time_tz() - timedelta(minutes=30)
                        ) > task.end_time
                        if is_30mins_ago:
                            set_ttlkv(
                                key_, json.dumps(session_), 10 * 60
                            )  # 3600 * 24 * 7
                        else:
                            set_ttlkv(key_, json.dumps(session_), 60)

                    except Exception as e:
                        print(f"Error processing task {task.id}: {str(e)}")
                        continue
                meta_data = {
                    "meta": {
                        "total_sessions": len(all_sessions),
                        "generated_at": get_shanghai_time().strftime(
                            "%Y-%m-%d %H:%M:%S"
                        ),
                    },
                    "sessions": all_sessions,
                }
                return meta_data
        except Exception as e:
            print(f"获取会话数据时发生异常: {str(e)}")
            raise HTTPException(status_code=500, detail=f"获取会话数据异常: {str(e)}")

    @staticmethod
    async def _fetch_tasks_to_process(
        task_ids: Optional[List[int]] = None,
    ) -> List[AnnotationTask]:
        """获取需要处理的任务列表"""
        async with get_session() as session:
            current_time = datetime.now()
            time_5_minutes_ago = current_time - timedelta(minutes=5)
            target_time = datetime(2025, 6, 3, 16, 0, 0)

            stmt = (
                select(AnnotationTask)
                .where(AnnotationTask.is_deleted == False)
                .where(AnnotationTask.status == TaskStatus.SUCCESS.value)
                .where(AnnotationTask.backfilled_annotation_id == None)
                .where(AnnotationTask.start_time != None)
                .where(AnnotationTask.end_time != None)
                .where(AnnotationTask.end_time <= time_5_minutes_ago)
                .where(AnnotationTask.end_time >= target_time)
                .order_by(AnnotationTask.created_at.desc())
            )

            if task_ids is not None and len(task_ids) > 0:
                stmt = stmt.where(AnnotationTask.id.in_(task_ids))
            result = await session.execute(stmt)
            return result.scalars().all()

    @staticmethod
    async def _process_single_task_for_backfill(
        task: AnnotationTask, session, rtx2uid: Dict[str, Any]
    ) -> Tuple[bool, Optional[Dict[str, Any]], Optional[str]]:
        try:
            user_info = rtx2uid.get(task.assignee, {})
            uin = user_info.get("uin")
            user_id = user_info.get("userid_")
            personal_userid = (
                user_info.get("personal_userid_")
                if user_info.get("personal_userid_")
                else user_info.get("userid_")
            )

            # 从CK中获取数据并解码
            print(
                f"任务 {task.id} 开始处理: app_id={task.app_id}, assignee={task.assignee}"
            )
            log(
                f"任务 {task.id} 开始处理: app_id={task.app_id}, assignee={task.assignee}",
                task.id,
            )
            operations = None
            try:
                operations = await SessionService._get_task_session_operations(
                    app_id=task.app_id,
                    user_id=user_id,
                    personal_userid=personal_userid,
                    start_time=task.start_time,
                    end_time=task.end_time,
                    task_id=task.id,
                )
            except Exception as e:
                error_type = type(e).__name__
                error_msg = str(e)
                print(f"任务 {task.id}: 获取会话操作数据时发生异常: {error_type}: {e}")
                if any(
                    keyword in error_msg.lower()
                    for keyword in [
                        "unexpected packet",
                        "connection",
                        "network",
                        "timeout",
                        "server",
                    ]
                ):
                    print(f"任务 {task.id}: 数据库连接问题，跳过此任务")
                    return False, None, f"数据库连接异常: {error_type}"
                else:
                    return False, None, f"获取会话数据异常: {str(e)}"
            if not operations:
                return False, None, "无会话数据"

            # 聚合行为，转换为和沙箱相同的标注数据格式
            annotation_request = None
            try:
                annotation_request = (
                    await SessionService.convert_session_operations_to_annotation_data(
                        operations=operations,
                        rtx=task.assignee,
                        app_id=task.app_id,
                        target_id=str(task.id),  # 使用task.id作为target_id
                        user_id=user_id,
                        instruction=task.query,
                        uin=uin,
                    )
                )
            except Exception as e:
                print(
                    f"任务 {task.id}: 转换标注数据时发生异常: {type(e).__name__}: {e}"
                )
                return False, None, f"转换数据异常: {str(e)}"
            if not annotation_request or not annotation_request.operations:
                return False, None, "转换后无有效操作"

            # 返回处理结果，包含原始时间和转换后的数据
            task_processing_record = {
                "original_start": task.start_time,
                "original_end": task.end_time,
                "annotation_request": annotation_request,
            }

            print(f"任务 {task.id}: 处理完成，等待最终验证")
            del operations
            return True, task_processing_record, None

        except Exception as e:
            return False, None, str(e)

    @staticmethod
    async def batch_create_tasks(requests: List[CreateTaskRequest]) -> List[int]:
        task_ids = []
        async with get_session() as session:
            for req in requests:
                task = AnnotationTask(
                    app_id=req.app_id,
                    app_name=req.app_name,
                    query=req.query,
                    status=TaskStatus.INITIAL.value,
                    tag=req.tag,
                )
                session.add(task)
                await session.flush()
                task_ids.append(task.id)
            await session.commit()
        return task_ids

    @staticmethod
    async def set_task_unprocessable(request: UnprocessableTaskRequest) -> bool:
        async with get_session() as session:
            result = await session.execute(
                select(AnnotationTask).where(AnnotationTask.id == request.task_id)
            )
            task = result.scalar_one_or_none()
            if not task:
                raise HTTPException(status_code=404, detail="Task not found")
            stmt = (
                update(AnnotationTask)
                .where(AnnotationTask.id == request.task_id)
                .values(status=TaskStatus.UNPROCESSABLE.value)
            )
            await session.execute(stmt)
            return True
